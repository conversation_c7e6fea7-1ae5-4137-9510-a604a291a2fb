name: 'Deploy with Backup'
description: 'Deploys application with automatic backup and rollback capabilities'
inputs:
  user:
    description: 'SSH user'
    required: true
  host:
    description: 'SSH host'
    required: true
  path:
    description: 'Deployment path'
    required: true
  package_name:
    description: 'Deployment package name'
    required: true
  environment:
    description: 'Environment name (staging/production)'
    required: true
runs:
  using: 'composite'
  steps:
    - name: Deploy with backup
      shell: bash
      env:
        USER: ${{ inputs.user }}
        HOST: ${{ inputs.host }}
        PATH: ${{ inputs.path }}
        PACKAGE_NAME: ${{ inputs.package_name }}
        ENVIRONMENT: ${{ inputs.environment }}
      run: |
        echo "🚀 Starting deployment to $ENVIRONMENT server..."
        
        # Create backup of current deployment
        echo "📦 Creating backup of current deployment..."
        ssh "$USER@$HOST" "
          cd $PATH &&
          if [ -d current ]; then
            timestamp=\$(date +%Y%m%d-%H%M%S)
            cp -r current backup-\$timestamp &&
            echo '✅ Backup created: backup-'\$timestamp
          else
            echo '⚠️ No current deployment found, skipping backup'
          fi
        "
        
        # Upload new deployment
        echo "📤 Uploading deployment package..."
        if ! scp deployment/$PACKAGE_NAME "$USER@$HOST:$PATH/"; then
          echo "❌ Failed to upload deployment package"
          exit 1
        fi
        
        # Extract and setup new deployment
        echo "📂 Extracting deployment package..."
        ssh "$USER@$HOST" "
          cd $PATH &&
          mkdir -p releases/\$(basename $PACKAGE_NAME .tar.gz | sed 's/.*-//') &&
          tar -xzf $PACKAGE_NAME -C releases/\$(basename $PACKAGE_NAME .tar.gz | sed 's/.*-//') &&
          rm $PACKAGE_NAME &&
          echo '✅ Deployment package extracted successfully'
        "
        
        echo "✅ Deployment completed successfully"
