name: 'Validate Deployment Secrets'
description: 'Validates that all required deployment secrets are configured'
inputs:
  environment:
    description: 'Deployment environment (staging or production)'
    required: true
  ssh_private_key:
    description: 'SSH private key secret'
    required: true
  host:
    description: 'Deployment host secret'
    required: true
  user:
    description: 'SSH user secret'
    required: true
  path:
    description: 'Deployment path secret'
    required: true
outputs:
  secrets-available:
    description: 'Whether all required secrets are available'
    value: ${{ steps.validate.outputs.secrets-available }}
runs:
  using: 'composite'
  steps:
    - name: Validate deployment secrets
      id: validate
      shell: bash
      env:
        SSH_PRIVATE_KEY: ${{ inputs.ssh_private_key }}
        HOST: ${{ inputs.host }}
        USER: ${{ inputs.user }}
        PATH: ${{ inputs.path }}
        ENVIRONMENT: ${{ inputs.environment }}
      run: |
        echo "🔍 Validating required secrets for $ENVIRONMENT deployment..."
        
        # Check if all required secrets are available
        MISSING_SECRETS=()
        
        if [[ -z "$SSH_PRIVATE_KEY" ]]; then
          MISSING_SECRETS+=("${ENVIRONMENT^^}_SSH_PRIVATE_KEY")
        fi
        
        if [[ -z "$HOST" ]]; then
          MISSING_SECRETS+=("${ENVIRONMENT^^}_HOST")
        fi
        
        if [[ -z "$USER" ]]; then
          MISSING_SECRETS+=("${ENVIRONMENT^^}_USER")
        fi
        
        if [[ -z "$PATH" ]]; then
          MISSING_SECRETS+=("${ENVIRONMENT^^}_PATH")
        fi
        
        if [[ ${#MISSING_SECRETS[@]} -eq 0 ]]; then
          echo "✅ All required secrets are configured for $ENVIRONMENT"
          echo "secrets-available=true" >> $GITHUB_OUTPUT
        else
          echo "❌ Missing required secrets for $ENVIRONMENT: ${MISSING_SECRETS[*]}"
          echo "Please configure the following secrets in repository settings:"
          for secret in "${MISSING_SECRETS[@]}"; do
            echo "  - $secret"
          done
          echo ""
          echo "📖 Setup Instructions:"
          echo "1. Go to repository Settings > Secrets and variables > Actions"
          echo "2. Add the missing secrets listed above"
          echo "3. Re-run this workflow"
          echo "secrets-available=false" >> $GITHUB_OUTPUT
          exit 1
        fi
