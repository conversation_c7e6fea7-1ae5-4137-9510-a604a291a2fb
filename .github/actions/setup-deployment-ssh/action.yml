name: 'Setup Deployment SSH'
description: 'Sets up SSH connection for deployment with validation and error handling'
inputs:
  ssh_private_key:
    description: 'SSH private key'
    required: true
  host:
    description: 'SSH host'
    required: true
  user:
    description: 'SSH user'
    required: true
  test_connection:
    description: 'Whether to test the SSH connection'
    required: false
    default: 'true'
runs:
  using: 'composite'
  steps:
    - name: Setup SSH connection
      shell: bash
      env:
        SSH_PRIVATE_KEY: ${{ inputs.ssh_private_key }}
        HOST: ${{ inputs.host }}
        USER: ${{ inputs.user }}
        TEST_CONNECTION: ${{ inputs.test_connection }}
      run: |
        echo "🔧 Setting up SSH connection..."
        
        # Validate inputs
        if [[ -z "$SSH_PRIVATE_KEY" ]]; then
          echo "❌ SSH private key is not provided"
          exit 1
        fi
        
        if [[ -z "$HOST" ]]; then
          echo "❌ SSH host is not provided"
          exit 1
        fi
        
        if [[ -z "$USER" ]]; then
          echo "❌ SSH user is not provided"
          exit 1
        fi
        
        # Setup SSH
        mkdir -p ~/.ssh
        echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa
        
        # Add host to known_hosts
        echo "🔍 Adding host to known_hosts..."
        ssh-keyscan -H "$HOST" >> ~/.ssh/known_hosts
        
        # Test connection if requested
        if [[ "$TEST_CONNECTION" == "true" ]]; then
          echo "🔍 Testing SSH connection..."
          if ! ssh -o ConnectTimeout=10 -o BatchMode=yes "$USER@$HOST" "echo 'SSH connection successful'"; then
            echo "❌ Failed to connect to server"
            exit 1
          fi
          echo "✅ SSH connection test successful"
        fi
        
        echo "✅ SSH setup completed successfully"
