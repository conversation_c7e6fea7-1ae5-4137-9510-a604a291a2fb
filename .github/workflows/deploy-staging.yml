name: Deploy to Staging

# Required Repository Secrets:
# - STAGING_SSH_PRIVATE_KEY: SSH private key for staging server access
# - STAGING_HOST: Staging server hostname/IP
# - STAGING_USER: SSH username for staging server
# - STAGING_PATH: Deployment path on staging server
#
# Required GitHub Environments:
# - staging: Staging environment with deployment protection rules
#
# Setup Instructions:
# 1. Go to repository Settings > Secrets and variables > Actions
# 2. Add the required secrets listed above
# 3. Go to Settings > Environments and create 'staging' environment
# 4. Configure deployment protection rules as needed

on:
  push:
    branches:
      - staging
  workflow_dispatch:
    inputs:
      force_deploy:
        description: 'Force deployment even if <PERSON><PERSON> fails'
        required: false
        default: false
        type: boolean

env:
  DEPLOYMENT_ENVIRONMENT: staging
  PHP_VERSION: '8.4'
  NODE_VERSION: '22'

jobs:
  validate-secrets:
    name: Validate Required Secrets
    runs-on: ubuntu-latest
    outputs:
      secrets-available: ${{ steps.check-secrets.outputs.secrets-available }}

    steps:
      - name: Check required secrets
        id: check-secrets
        env:
          STAGING_SSH_PRIVATE_KEY: ${{ secrets.STAGING_SSH_PRIVATE_KEY }}
          STAGING_HOST: ${{ secrets.STAGING_HOST }}
          STAGING_USER: ${{ secrets.STAGING_USER }}
          STAGING_PATH: ${{ secrets.STAGING_PATH }}
        run: |
          echo "🔍 Validating required secrets for staging deployment..."

          # Check if all required secrets are available
          MISSING_SECRETS=()

          if [[ -z "$STAGING_SSH_PRIVATE_KEY" ]]; then
            MISSING_SECRETS+=("STAGING_SSH_PRIVATE_KEY")
          fi

          if [[ -z "$STAGING_HOST" ]]; then
            MISSING_SECRETS+=("STAGING_HOST")
          fi

          if [[ -z "$STAGING_USER" ]]; then
            MISSING_SECRETS+=("STAGING_USER")
          fi

          if [[ -z "$STAGING_PATH" ]]; then
            MISSING_SECRETS+=("STAGING_PATH")
          fi

          if [[ ${#MISSING_SECRETS[@]} -eq 0 ]]; then
            echo "✅ All required secrets are configured"
            echo "secrets-available=true" >> $GITHUB_OUTPUT
          else
            echo "❌ Missing required secrets: ${MISSING_SECRETS[*]}"
            echo "Please configure the following secrets in repository settings:"
            for secret in "${MISSING_SECRETS[@]}"; do
              echo "  - $secret"
            done
            echo ""
            echo "📖 Setup Instructions:"
            echo "1. Go to repository Settings > Secrets and variables > Actions"
            echo "2. Add the missing secrets listed above"
            echo "3. Re-run this workflow"
            echo "secrets-available=false" >> $GITHUB_OUTPUT
            exit 1
          fi

  pre-deployment-checks:
    name: Pre-deployment Checks
    runs-on: ubuntu-latest
    needs: validate-secrets
    if: needs.validate-secrets.outputs.secrets-available == 'true'
    outputs:
      should-deploy: ${{ steps.check.outputs.should-deploy }}
      deployment_id: ${{ steps.deployment.outputs.deployment_id }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check CI status
        id: check
        run: |
          if [[ "${{ github.event.inputs.force_deploy }}" == "true" ]]; then
            echo "should-deploy=true" >> $GITHUB_OUTPUT
            echo "⚠️ Force deployment enabled - skipping CI checks"
          else
            # In a real scenario, you would check the CI workflow status
            echo "should-deploy=true" >> $GITHUB_OUTPUT
            echo "✅ CI checks passed - proceeding with deployment"
          fi

      - name: Create deployment
        id: deployment
        uses: actions/github-script@v7
        with:
          script: |
            const deployment = await github.rest.repos.createDeployment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: context.sha,
              environment: 'staging',
              description: 'Deploying to staging environment',
              auto_merge: false,
              required_contexts: []
            });
            core.setOutput('deployment_id', deployment.data.id);
            return deployment.data.id;

  build-for-staging:
    name: Build for Staging
    runs-on: ubuntu-latest
    needs: [validate-secrets, pre-deployment-checks]
    if: needs.validate-secrets.outputs.secrets-available == 'true' && needs.pre-deployment-checks.outputs.should-deploy == 'true'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ env.PHP_VERSION }}
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, pdo_mysql, bcmath, soap, intl, gd, exif, iconv
          tools: composer:v2

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install Composer dependencies
        run: composer install --no-interaction --prefer-dist --optimize-autoloader --no-dev

      - name: Install Node dependencies
        run: npm ci

      - name: Build production assets
        run: npm run build

      - name: Create staging deployment package
        run: |
          mkdir -p deployment
          tar -czf deployment/ecommflex-staging-${{ github.sha }}.tar.gz \
            --exclude=node_modules \
            --exclude=.git \
            --exclude=tests \
            --exclude=storage/logs \
            --exclude=storage/framework/cache \
            --exclude=storage/framework/sessions \
            --exclude=storage/framework/views \
            --exclude=.env \
            .

      - name: Upload deployment package
        uses: actions/upload-artifact@v4
        with:
          name: staging-deployment-${{ github.sha }}
          path: deployment/
          retention-days: 7

  deploy-to-staging:
    name: Deploy to Staging Server
    runs-on: ubuntu-latest
    needs: [validate-secrets, pre-deployment-checks, build-for-staging]
    if: needs.validate-secrets.outputs.secrets-available == 'true'
    environment:
      name: staging
      url: https://staging.ecommflex.com
    
    steps:
      - name: Download deployment package
        uses: actions/download-artifact@v4
        with:
          name: staging-deployment-${{ github.sha }}
          path: deployment/

      - name: Update deployment status (in progress)
        uses: actions/github-script@v7
        with:
          script: |
            await github.rest.repos.createDeploymentStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              deployment_id: ${{ needs.pre-deployment-checks.outputs.deployment_id }},
              state: 'in_progress',
              description: 'Deployment to staging in progress'
            });

      - name: Setup SSH
        env:
          STAGING_SSH_PRIVATE_KEY: ${{ secrets.STAGING_SSH_PRIVATE_KEY }}
          STAGING_HOST: ${{ secrets.STAGING_HOST }}
        run: |
          echo "🔧 Setting up SSH connection to staging server..."

          # Validate SSH key
          if [[ -z "$STAGING_SSH_PRIVATE_KEY" ]]; then
            echo "❌ STAGING_SSH_PRIVATE_KEY secret is not configured"
            exit 1
          fi

          if [[ -z "$STAGING_HOST" ]]; then
            echo "❌ STAGING_HOST secret is not configured"
            exit 1
          fi

          # Setup SSH
          mkdir -p ~/.ssh
          echo "$STAGING_SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa

          # Add host to known_hosts
          echo "🔍 Adding staging host to known_hosts..."
          ssh-keyscan -H "$STAGING_HOST" >> ~/.ssh/known_hosts

          echo "✅ SSH setup completed successfully"

      - name: Deploy to staging server
        env:
          STAGING_USER: ${{ secrets.STAGING_USER }}
          STAGING_HOST: ${{ secrets.STAGING_HOST }}
          STAGING_PATH: ${{ secrets.STAGING_PATH }}
        run: |
          echo "🚀 Starting deployment to staging server..."

          # Validate deployment secrets
          if [[ -z "$STAGING_USER" || -z "$STAGING_HOST" || -z "$STAGING_PATH" ]]; then
            echo "❌ Missing required deployment secrets"
            exit 1
          fi

          # Test SSH connection
          echo "🔍 Testing SSH connection..."
          if ! ssh -o ConnectTimeout=10 -o BatchMode=yes "$STAGING_USER@$STAGING_HOST" "echo 'SSH connection successful'"; then
            echo "❌ Failed to connect to staging server"
            exit 1
          fi

          # Create backup of current deployment
          echo "📦 Creating backup of current deployment..."
          ssh "$STAGING_USER@$STAGING_HOST" "
            cd $STAGING_PATH &&
            if [ -d current ]; then
              cp -r current backup-$(date +%Y%m%d-%H%M%S) &&
              echo '✅ Backup created successfully'
            else
              echo '⚠️ No current deployment found, skipping backup'
            fi
          "

          # Upload new deployment
          echo "📤 Uploading deployment package..."
          if ! scp deployment/ecommflex-staging-${{ github.sha }}.tar.gz "$STAGING_USER@$STAGING_HOST:$STAGING_PATH/"; then
            echo "❌ Failed to upload deployment package"
            exit 1
          fi

          # Extract and setup new deployment
          echo "📂 Extracting deployment package..."
          ssh "$STAGING_USER@$STAGING_HOST" "
            cd $STAGING_PATH &&
            mkdir -p releases/${{ github.sha }} &&
            tar -xzf ecommflex-staging-${{ github.sha }}.tar.gz -C releases/${{ github.sha }} &&
            rm ecommflex-staging-${{ github.sha }}.tar.gz &&
            echo '✅ Deployment package extracted successfully'
          "

      - name: Setup environment and dependencies
        env:
          STAGING_USER: ${{ secrets.STAGING_USER }}
          STAGING_HOST: ${{ secrets.STAGING_HOST }}
          STAGING_PATH: ${{ secrets.STAGING_PATH }}
        run: |
          echo "⚙️ Setting up environment and dependencies..."
          ssh "$STAGING_USER@$STAGING_HOST" "
            cd $STAGING_PATH/releases/${{ github.sha }} &&
            echo '📋 Copying environment configuration...' &&
            cp $STAGING_PATH/.env.staging .env &&
            echo '📦 Installing Composer dependencies...' &&
            composer install --no-interaction --prefer-dist --optimize-autoloader --no-dev &&
            echo '🔧 Caching Laravel configuration...' &&
            php artisan config:cache &&
            php artisan route:cache &&
            php artisan view:cache &&
            echo '✅ Environment setup completed successfully'
          "

      - name: Run database migrations
        env:
          STAGING_USER: ${{ secrets.STAGING_USER }}
          STAGING_HOST: ${{ secrets.STAGING_HOST }}
          STAGING_PATH: ${{ secrets.STAGING_PATH }}
        run: |
          echo "🗄️ Running database migrations..."
          ssh "$STAGING_USER@$STAGING_HOST" "
            cd $STAGING_PATH/releases/${{ github.sha }} &&
            echo '📊 Running database migrations...' &&
            php artisan migrate --force &&
            echo '✅ Database migrations completed successfully'
          "

      - name: Create symlinks and switch deployment
        env:
          STAGING_USER: ${{ secrets.STAGING_USER }}
          STAGING_HOST: ${{ secrets.STAGING_HOST }}
          STAGING_PATH: ${{ secrets.STAGING_PATH }}
        run: |
          echo "🔗 Creating symlinks and switching deployment..."
          ssh "$STAGING_USER@$STAGING_HOST" "
            cd $STAGING_PATH &&
            echo '🔗 Creating storage symlink...' &&
            ln -nfs $STAGING_PATH/storage releases/${{ github.sha }}/storage &&
            echo '🔄 Performing atomic deployment switch...' &&
            ln -nfs releases/${{ github.sha }} current-new &&
            mv current-new current &&
            echo '✅ Deployment switch completed successfully'
          "

      - name: Restart services
        env:
          STAGING_USER: ${{ secrets.STAGING_USER }}
          STAGING_HOST: ${{ secrets.STAGING_HOST }}
          STAGING_PATH: ${{ secrets.STAGING_PATH }}
        run: |
          echo "🔄 Restarting services..."
          ssh "$STAGING_USER@$STAGING_HOST" "
            echo '🔄 Reloading PHP-FPM...' &&
            sudo systemctl reload php8.4-fpm &&
            echo '🔄 Reloading Nginx...' &&
            sudo systemctl reload nginx &&
            cd $STAGING_PATH/current &&
            echo '🔄 Restarting queue workers...' &&
            php artisan queue:restart &&
            echo '✅ All services restarted successfully'
          "

  health-check:
    name: Health Check
    runs-on: ubuntu-latest
    needs: [validate-secrets, pre-deployment-checks, deploy-to-staging]
    if: needs.validate-secrets.outputs.secrets-available == 'true'
    
    steps:
      - name: Wait for application to start
        run: sleep 30

      - name: Check application health
        run: |
          response=$(curl -s -o /dev/null -w "%{http_code}" https://staging.ecommflex.com/health)
          if [ $response -eq 200 ]; then
            echo "✅ Health check passed"
          else
            echo "❌ Health check failed with status: $response"
            exit 1
          fi

      - name: Run smoke tests
        run: |
          # Basic functionality tests
          curl -f https://staging.ecommflex.com/ || exit 1
          curl -f https://staging.ecommflex.com/api/health || exit 1
          echo "✅ Smoke tests passed"

      - name: Update deployment status (success)
        if: success()
        uses: actions/github-script@v7
        with:
          script: |
            await github.rest.repos.createDeploymentStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              deployment_id: ${{ needs.pre-deployment-checks.outputs.deployment_id }},
              state: 'success',
              description: 'Deployment to staging successful',
              environment_url: 'https://staging.ecommflex.com'
            });

      - name: Update deployment status (failure)
        if: failure()
        uses: actions/github-script@v7
        with:
          script: |
            await github.rest.repos.createDeploymentStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              deployment_id: ${{ needs.pre-deployment-checks.outputs.deployment_id }},
              state: 'failure',
              description: 'Deployment to staging failed'
            });

  rollback:
    name: Rollback on Failure
    runs-on: ubuntu-latest
    needs: [validate-secrets, pre-deployment-checks, deploy-to-staging, health-check]
    if: failure() && needs.validate-secrets.outputs.secrets-available == 'true'

    steps:
      - name: Setup SSH for rollback
        env:
          STAGING_SSH_PRIVATE_KEY: ${{ secrets.STAGING_SSH_PRIVATE_KEY }}
          STAGING_HOST: ${{ secrets.STAGING_HOST }}
        run: |
          echo "🔧 Setting up SSH for rollback..."
          mkdir -p ~/.ssh
          echo "$STAGING_SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H "$STAGING_HOST" >> ~/.ssh/known_hosts
          echo "✅ SSH setup for rollback completed"

      - name: Rollback to previous version
        env:
          STAGING_USER: ${{ secrets.STAGING_USER }}
          STAGING_HOST: ${{ secrets.STAGING_HOST }}
          STAGING_PATH: ${{ secrets.STAGING_PATH }}
        run: |
          echo "🔄 Starting rollback to previous version..."
          ssh "$STAGING_USER@$STAGING_HOST" "
            cd $STAGING_PATH &&
            if [ -d backup-* ]; then
              latest_backup=\$(ls -t backup-* | head -n1)
              echo '🔄 Rolling back to: '\$latest_backup
              ln -nfs \$latest_backup current-rollback
              mv current-rollback current
              echo '🔄 Reloading services after rollback...'
              sudo systemctl reload php8.4-fpm
              sudo systemctl reload nginx
              echo '✅ Rollback completed successfully'
            else
              echo '❌ No backup found for rollback'
              exit 1
            fi
          "

  cleanup:
    name: Cleanup Old Deployments
    runs-on: ubuntu-latest
    needs: [validate-secrets, health-check]
    if: success() && needs.validate-secrets.outputs.secrets-available == 'true'

    steps:
      - name: Setup SSH for cleanup
        env:
          STAGING_SSH_PRIVATE_KEY: ${{ secrets.STAGING_SSH_PRIVATE_KEY }}
          STAGING_HOST: ${{ secrets.STAGING_HOST }}
        run: |
          echo "🔧 Setting up SSH for cleanup..."
          mkdir -p ~/.ssh
          echo "$STAGING_SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H "$STAGING_HOST" >> ~/.ssh/known_hosts
          echo "✅ SSH setup for cleanup completed"

      - name: Clean up old releases
        env:
          STAGING_USER: ${{ secrets.STAGING_USER }}
          STAGING_HOST: ${{ secrets.STAGING_HOST }}
          STAGING_PATH: ${{ secrets.STAGING_PATH }}
        run: |
          echo "🧹 Cleaning up old deployments..."
          ssh "$STAGING_USER@$STAGING_HOST" "
            echo '🗂️ Cleaning up old releases (keeping latest 5)...' &&
            cd $STAGING_PATH/releases &&
            ls -t | tail -n +6 | xargs rm -rf &&
            echo '🗂️ Cleaning up old backups (older than 7 days)...' &&
            cd $STAGING_PATH &&
            find . -name 'backup-*' -mtime +7 -delete &&
            echo '✅ Cleanup completed successfully'
          "

  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [validate-secrets, pre-deployment-checks, deploy-to-staging, health-check]
    if: always() && needs.validate-secrets.outputs.secrets-available == 'true'
    
    steps:
      - name: Notify success
        if: needs.health-check.result == 'success'
        run: |
          echo "✅ Staging deployment successful!"
          echo "Environment: https://staging.ecommflex.com"
          echo "Commit: ${{ github.sha }}"

      - name: Notify failure
        if: needs.deploy-to-staging.result == 'failure' || needs.health-check.result == 'failure'
        run: |
          echo "❌ Staging deployment failed!"
          echo "Commit: ${{ github.sha }}"
          exit 1
